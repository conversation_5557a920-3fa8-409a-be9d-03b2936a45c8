package payment

import (
	"testing"
)

func TestMomoVerifyCredentials(t *testing.T) {
	client := NewMomoClient()

	// Test with invalid credentials - should return error
	t.Run("Invalid Credentials", func(t *testing.T) {
		err := client.VerifyCredentials("invalid_partner", "invalid_access", "invalid_secret")
		if err == nil {
			t.<PERSON>rror("Expected error for invalid credentials, got nil")
		}
	})

	// Test with empty credentials - should return error
	t.Run("Empty Credentials", func(t *testing.T) {
		err := client.VerifyCredentials("", "", "")
		if err == nil {
			t.Error("Expected error for empty credentials, got nil")
		}
	})

	// Note: For testing with real credentials, you would need valid MoMo test credentials
	// This test demonstrates the verification function structure
}

func TestMomoClient(t *testing.T) {
	client := NewMomoClient()
	
	if client == nil {
		t.Fatal("Failed to create MoMo client")
	}

	if client.GetProviderName() != "momo" {
		t.<PERSON>("Expected provider name 'momo', got '%s'", client.GetProviderName())
	}

	if client.BaseURL != "https://payment.momo.vn" {
		t.Errorf("Expected base URL 'https://payment.momo.vn', got '%s'", client.BaseURL)
	}
}
