package payment

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/samber/lo"
)

// MomoClient represents a MOMO payment client
type MomoClient struct {
	BaseURL    string
	HTTPClient *resty.Client
}

// MomoRequest represents a MOMO payment request
type MomoRequest struct {
	PartnerCode string `json:"partnerCode"`
	AccessKey   string `json:"accessKey"`
	RequestID   string `json:"requestId"`
	Amount      int64  `json:"amount"`
	OrderID     string `json:"orderId"`
	OrderInfo   string `json:"orderInfo"`
	RedirectURL string `json:"redirectUrl"`
	IpnURL      string `json:"ipnUrl"`
	RequestType string `json:"requestType"`
	ExtraData   string `json:"extraData"`
	Lang        string `json:"lang"`
	Signature   string `json:"signature"`
}

// MomoResponse represents a MOMO payment response
type MomoResponse struct {
	PartnerCode     string `json:"partnerCode"`
	OrderID         string `json:"orderId"`
	RequestID       string `json:"requestId"`
	Amount          int64  `json:"amount"`
	ResponseTime    int64  `json:"responseTime"`
	Message         string `json:"message"`
	ResultCode      int    `json:"resultCode"`
	PayURL          string `json:"payUrl"`
	Deeplink        string `json:"deeplink"`
	QRCodeURL       string `json:"qrCodeUrl"`
	AppLink         string `json:"applink"`
	DeeplinkMiniApp string `json:"deeplinkMiniApp"`
}

// MomoStatusRequest represents a MOMO transaction status request
type MomoStatusRequest struct {
	PartnerCode string `json:"partnerCode"`
	AccessKey   string `json:"accessKey"`
	RequestID   string `json:"requestId"`
	OrderID     string `json:"orderId"`
	RequestType string `json:"requestType"`
	Signature   string `json:"signature"`
	Lang        string `json:"lang"`
}

// MomoStatusResponse represents a MOMO transaction status response
type MomoStatusResponse struct {
	PartnerCode  string `json:"partnerCode"`
	OrderID      string `json:"orderId"`
	RequestID    string `json:"requestId"`
	ExtraData    string `json:"extraData"`
	Amount       int64  `json:"amount"`
	TransID      int64  `json:"transId"`
	PayType      string `json:"payType"`
	ResultCode   int    `json:"resultCode"`
	RefundTrans  []any  `json:"refundTrans"`
	Message      string `json:"message"`
	ResponseTime int64  `json:"responseTime"`
	LastUpdated  int64  `json:"lastUpdated"`
}

// NewMomoClient creates a new MOMO payment client
func NewMomoClient() *MomoClient {
	return &MomoClient{
		BaseURL:    lo.If(os.Getenv("NODE_ENV") == "prod", "https://payment.momo.vn").Else("https://test-payment.momo.vn"),
		HTTPClient: resty.New(),
	}
}

// GetProviderName returns the provider name
func (c *MomoClient) GetProviderName() string {
	return "momo"
}

// CreatePaymentLink creates a new MOMO payment transaction
func (c *MomoClient) CreatePaymentLink(token *models.Token, request *PaymentRequest) (*PaymentResponse, error) {
	// Generate transaction ID
	transactionID := request.OrderID + "_" + strings.Split(uuid.New().String(), "-")[0]

	// Determine request type based on payment method
	requestType := c.getRequestType(request.PaymentMethod)

	// Prepare MOMO request
	momoReq := MomoRequest{
		PartnerCode: token.SiteID,
		AccessKey:   token.Username,
		RequestID:   transactionID,
		Amount:      int64(request.Amount),
		OrderID:     transactionID,
		OrderInfo:   fmt.Sprintf("Thanh toán momo cho order %s", request.OrderID),
		RedirectURL: request.ClientCallback,
		IpnURL:      request.ServerCallback,
		RequestType: requestType,
		ExtraData:   request.ExtraData,
		Lang:        c.getLanguage(request.Language),
	}

	// Generate signature
	signature := c.generateSignature(momoReq, "captureMoMoWallet", token.Password)
	momoReq.Signature = signature

	// Remove access key from request body
	momoReq.AccessKey = ""

	// Send request
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(momoReq).
		Post(c.BaseURL + "/v2/gateway/api/create")

	if err != nil {
		return nil, fmt.Errorf("failed to create MOMO payment: %w", err)
	}

	// Parse response
	var momoResp MomoResponse
	if err := json.Unmarshal(resp.Body(), &momoResp); err != nil {
		return nil, fmt.Errorf("failed to parse MOMO response: %w", err)
	}

	// Check result code
	if momoResp.ResultCode != 0 {
		return nil, fmt.Errorf("MOMO payment creation failed: %s", momoResp.Message)
	}

	return &PaymentResponse{
		Success:       true,
		TransactionID: momoResp.OrderID,
		PayURL:        momoResp.PayURL,
		QRCode:        momoResp.QRCodeURL,
		Deeplink:      momoResp.Deeplink,
		Message:       momoResp.Message,
		Data:          momoResp,
	}, nil
}

// GetTransactionStatus retrieves MOMO transaction status
func (c *MomoClient) GetTransactionStatus(token *models.Token, transactionID string) (*TransactionStatus, error) {
	statusReq := MomoStatusRequest{
		PartnerCode: token.SiteID,
		AccessKey:   token.Username,
		RequestID:   transactionID,
		OrderID:     transactionID,
		RequestType: "transactionStatus",
		Lang:        "vi",
	}

	// Generate signature
	signature := c.generateStatusSignature(statusReq, token.Password)
	statusReq.Signature = signature

	// Send request
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(statusReq).
		Post(c.BaseURL + "/v2/gateway/api/create")

	if err != nil {
		return nil, fmt.Errorf("failed to get MOMO transaction status: %w", err)
	}

	// Parse response
	var statusResp MomoStatusResponse
	if err := json.Unmarshal(resp.Body(), &statusResp); err != nil {
		return nil, fmt.Errorf("failed to parse MOMO status response: %w", err)
	}

	status := c.mapStatus(statusResp.ResultCode)

	return &TransactionStatus{
		TransactionID: statusResp.OrderID,
		OrderID:       statusResp.OrderID,
		Amount:        float64(statusResp.Amount),
		Status:        status,
		Message:       statusResp.Message,
		CreatedAt:     time.Unix(statusResp.ResponseTime/1000, 0),
		UpdatedAt:     time.Unix(statusResp.LastUpdated/1000, 0),
		Data:          statusResp,
	}, nil
}

// ProcessCallback processes MOMO callback data
func (c *MomoClient) ProcessCallback(data map[string]interface{}) (*CallbackResponse, error) {
	orderID, _ := data["orderId"].(string)
	resultCode, _ := data["resultCode"].(float64)
	amount, _ := data["amount"].(float64)
	message, _ := data["message"].(string)

	status := c.mapStatus(int(resultCode))

	return &CallbackResponse{
		Success:       resultCode == 0 || resultCode == 9000,
		TransactionID: orderID,
		OrderID:       orderID,
		Status:        status,
		Amount:        amount,
		Message:       message,
		Data:          data,
	}, nil
}

// ValidateSignature validates MOMO callback signature
func (c *MomoClient) ValidateSignature(token *models.Token, data map[string]interface{}, signature string) bool {
	// Implementation would depend on MOMO's signature validation requirements
	// This is a placeholder implementation
	return true
}

// Helper methods
func (c *MomoClient) getRequestType(paymentMethod string) string {
	switch paymentMethod {
	case "MOMO":
		return "captureWallet"
	case "MOMO_VTS":
		return "payWithVTS"
	case "MOMO_ATM":
		return "payWithATM"
	case "MOMO_MOD":
		return "onDelivery"
	default:
		return "captureWallet"
	}
}

func (c *MomoClient) getLanguage(lang string) string {
	if lang == "" {
		return "vi"
	}
	return lang
}

func (c *MomoClient) mapStatus(resultCode int) string {
	switch resultCode {
	case 0, 9000:
		return "COMPLETED"
	case 1006:
		return "CANCELLED"
	default:
		return "PENDING"
	}
}

func (c *MomoClient) generateSignature(req MomoRequest, operation, secretKey string) string {
	// Define field order for signature generation
	fields := []string{"accessKey", "amount", "extraData", "ipnUrl", "orderId", "orderInfo", "partnerCode", "redirectUrl", "requestId", "requestType"}

	var parts []string
	for _, field := range fields {
		switch field {
		case "accessKey":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.AccessKey))
		case "amount":
			parts = append(parts, fmt.Sprintf("%s=%d", field, req.Amount))
		case "extraData":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.ExtraData))
		case "ipnUrl":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.IpnURL))
		case "orderId":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.OrderID))
		case "orderInfo":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.OrderInfo))
		case "partnerCode":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.PartnerCode))
		case "redirectUrl":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.RedirectURL))
		case "requestId":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.RequestID))
		case "requestType":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.RequestType))
		}
	}

	rawSignature := strings.Join(parts, "&")
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(rawSignature))
	return hex.EncodeToString(h.Sum(nil))
}

func (c *MomoClient) generateStatusSignature(req MomoStatusRequest, secretKey string) string {
	fields := []string{"partnerCode", "accessKey", "requestId", "orderId", "requestType"}

	var parts []string
	for _, field := range fields {
		switch field {
		case "partnerCode":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.PartnerCode))
		case "accessKey":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.AccessKey))
		case "requestId":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.RequestID))
		case "orderId":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.OrderID))
		case "requestType":
			parts = append(parts, fmt.Sprintf("%s=%s", field, req.RequestType))
		}
	}

	rawSignature := strings.Join(parts, "&")
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(rawSignature))
	return hex.EncodeToString(h.Sum(nil))
}
